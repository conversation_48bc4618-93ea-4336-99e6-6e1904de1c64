import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CalendarIcon, 
  UserGroupIcon, 
  CurrencyDollarIcon,
  ChartBarIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  ChatBubbleLeftRightIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { useAuth } from '../contexts/AuthContext';
import { useBooking } from '../contexts/BookingContext';
import { Booking } from '../types/booking';
import DashboardCard from '../components/dashboard/DashboardCard';
import StatsCard from '../components/dashboard/StatsCard';
import DataTable, { TableColumn } from '../components/dashboard/DataTable';
import StatusBadge from '../components/dashboard/StatusBadge';
import Button from '../components/ui/Button';
import SEOHead from '../components/seo/SEOHead';

const StaffDashboard: React.FC = () => {
  const { user } = useAuth();
  const { bookings, getBookingStats, isLoading } = useBooking();
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);

  if (!user || (user.role !== 'staff' && user.role !== 'admin')) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-moon-navy mb-4">Access Denied</h2>
          <p className="text-moon-gray">This area is restricted to staff members only.</p>
        </div>
      </div>
    );
  }

  const stats = getBookingStats();
  
  // Get recent bookings and upcoming events
  const recentBookings = bookings
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    .slice(0, 10);

  const upcomingEvents = bookings
    .filter(booking => {
      const eventDate = new Date(booking.eventDate);
      const today = new Date();
      return eventDate >= today && booking.status === 'confirmed';
    })
    .sort((a, b) => new Date(a.eventDate).getTime() - new Date(b.eventDate).getTime())
    .slice(0, 5);

  const pendingBookings = bookings.filter(booking => booking.status === 'pending');

  const columns: TableColumn<Booking>[] = [
    {
      key: 'eventDetails.title',
      label: 'Event',
      sortable: true,
      render: (booking) => (
        <div>
          <div className="font-medium text-moon-navy">{booking.eventDetails.title}</div>
          <div className="text-sm text-moon-gray">{booking.contactPerson.firstName} {booking.contactPerson.lastName}</div>
        </div>
      )
    },
    {
      key: 'eventDate',
      label: 'Date',
      sortable: true,
      render: (booking) => (
        <div>
          <div className="font-medium text-moon-navy">
            {new Date(booking.eventDate).toLocaleDateString()}
          </div>
          <div className="text-sm text-moon-gray">{booking.eventTime}</div>
        </div>
      )
    },
    {
      key: 'eventType',
      label: 'Type',
      sortable: true,
      render: (booking) => (
        <span className="capitalize">{booking.eventType}</span>
      )
    },
    {
      key: 'guestCount',
      label: 'Guests',
      sortable: true
    },
    {
      key: 'status',
      label: 'Status',
      sortable: true,
      render: (booking) => (
        <StatusBadge status={booking.status} variant="booking" />
      )
    },
    {
      key: 'totalAmount',
      label: 'Value',
      sortable: true,
      render: (booking) => `$${booking.totalAmount.toLocaleString()}`
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-moon-navy/5 to-moon-silver/10 py-8">
      <SEOHead
        title="Staff Dashboard - Moon Event Center"
        description="Staff dashboard for managing events and customers at Moon Event Center."
        keywords={['staff', 'dashboard', 'management', 'Moon Event Center']}
        url="https://mooneventcenter.com/staff"
        noIndex={true}
      />

      <div className="container-max">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          {/* Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-serif font-bold text-moon-navy mb-2">
              Staff Dashboard
            </h1>
            <p className="text-moon-gray">
              Welcome back, {user.firstName}! Here's what's happening today.
            </p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <StatsCard
              title="Total Bookings"
              value={stats.total}
              icon={CalendarIcon}
              color="blue"
              loading={isLoading}
            />
            <StatsCard
              title="Pending Approval"
              value={stats.pending}
              icon={ClockIcon}
              color="yellow"
              loading={isLoading}
            />
            <StatsCard
              title="Total Revenue"
              value={`$${stats.totalRevenue.toLocaleString()}`}
              icon={CurrencyDollarIcon}
              color="green"
              loading={isLoading}
            />
            <StatsCard
              title="Upcoming Events"
              value={upcomingEvents.length}
              icon={ChartBarIcon}
              color="purple"
              loading={isLoading}
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* Pending Bookings Alert */}
              {pendingBookings.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="bg-yellow-50 border border-yellow-200 rounded-lg p-4"
                >
                  <div className="flex items-center space-x-3">
                    <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600" />
                    <div>
                      <h3 className="font-medium text-yellow-800">
                        {pendingBookings.length} Booking{pendingBookings.length !== 1 ? 's' : ''} Awaiting Approval
                      </h3>
                      <p className="text-sm text-yellow-700">
                        Review and approve pending bookings to confirm events.
                      </p>
                    </div>
                  </div>
                </motion.div>
              )}

              {/* Recent Bookings */}
              <DashboardCard title="Recent Bookings" loading={isLoading}>
                <DataTable
                  data={recentBookings}
                  columns={columns}
                  loading={isLoading}
                  emptyMessage="No bookings found"
                  onRowClick={(booking) => setSelectedBooking(booking)}
                />
              </DashboardCard>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Quick Actions */}
              <DashboardCard title="Quick Actions">
                <div className="space-y-3">
                  <Button
                    variant="primary"
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <CalendarIcon className="w-4 h-4" />
                    <span>View Calendar</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <UserGroupIcon className="w-4 h-4" />
                    <span>Manage Customers</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <PhotoIcon className="w-4 h-4" />
                    <span>Update Gallery</span>
                  </Button>
                  
                  <Button
                    variant="outline"
                    className="w-full flex items-center justify-center space-x-2"
                  >
                    <ChatBubbleLeftRightIcon className="w-4 h-4" />
                    <span>Messages</span>
                  </Button>
                </div>
              </DashboardCard>

              {/* Upcoming Events */}
              <DashboardCard title="Upcoming Events">
                {upcomingEvents.length === 0 ? (
                  <p className="text-moon-gray text-sm">No upcoming events</p>
                ) : (
                  <div className="space-y-3">
                    {upcomingEvents.map((event) => (
                      <div
                        key={event.id}
                        className="p-3 bg-moon-silver/5 rounded-lg border border-moon-silver/20"
                      >
                        <div className="font-medium text-moon-navy text-sm">
                          {event.eventDetails.title}
                        </div>
                        <div className="text-xs text-moon-gray">
                          {new Date(event.eventDate).toLocaleDateString()} at {event.eventTime}
                        </div>
                        <div className="text-xs text-moon-gray">
                          {event.guestCount} guests • {event.contactPerson.firstName} {event.contactPerson.lastName}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </DashboardCard>

              {/* System Status */}
              <DashboardCard title="System Status">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-moon-gray">Website</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Online
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-moon-gray">Booking System</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Active
                    </span>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-moon-gray">Payment Processing</span>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Operational
                    </span>
                  </div>
                </div>
              </DashboardCard>
            </div>
          </div>
        </motion.div>
      </div>

      {/* Booking Details Modal */}
      {selectedBooking && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
          >
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <h3 className="text-2xl font-bold text-moon-navy">
                  {selectedBooking.eventDetails.title}
                </h3>
                <button
                  onClick={() => setSelectedBooking(null)}
                  className="text-moon-gray hover:text-moon-navy"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-semibold text-moon-navy mb-3">Event Details</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-moon-gray">Type:</span> <span className="capitalize">{selectedBooking.eventType}</span></div>
                    <div><span className="text-moon-gray">Date:</span> {new Date(selectedBooking.eventDate).toLocaleDateString()}</div>
                    <div><span className="text-moon-gray">Time:</span> {selectedBooking.eventTime}</div>
                    <div><span className="text-moon-gray">Guests:</span> {selectedBooking.guestCount}</div>
                    <div><span className="text-moon-gray">Package:</span> <span className="capitalize">{selectedBooking.packageType}</span></div>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-moon-navy mb-3">Contact Information</h4>
                  <div className="space-y-2 text-sm">
                    <div><span className="text-moon-gray">Name:</span> {selectedBooking.contactPerson.firstName} {selectedBooking.contactPerson.lastName}</div>
                    <div><span className="text-moon-gray">Email:</span> {selectedBooking.contactPerson.email}</div>
                    <div><span className="text-moon-gray">Phone:</span> {selectedBooking.contactPerson.phone}</div>
                  </div>
                </div>
              </div>

              <div className="mt-8 flex justify-end space-x-4">
                <Button
                  variant="outline"
                  onClick={() => setSelectedBooking(null)}
                >
                  Close
                </Button>
                {selectedBooking.status === 'pending' && (
                  <>
                    <Button variant="outline" className="text-red-600 border-red-300">
                      Decline
                    </Button>
                    <Button variant="primary">
                      Approve
                    </Button>
                  </>
                )}
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </div>
  );
};

export default StaffDashboard;
