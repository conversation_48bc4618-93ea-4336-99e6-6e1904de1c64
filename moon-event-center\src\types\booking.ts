export interface Booking {
  id: string;
  userId: string;
  eventType: 'wedding' | 'corporate' | 'birthday' | 'anniversary' | 'quinceañera' | 'other';
  eventDate: string;
  eventTime: string;
  guestCount: number;
  packageType: 'platinum' | 'gold' | 'silver' | 'custom';
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed';
  totalAmount: number;
  paidAmount: number;
  paymentStatus: 'pending' | 'partial' | 'paid' | 'refunded';
  contactPerson: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  eventDetails: {
    title: string;
    description?: string;
    specialRequests?: string;
    cateringPreference?: string;
    decorationTheme?: string;
    musicRequirements?: string;
  };
  venue: {
    mainHall: boolean;
    bridalRoom: boolean;
    additionalRooms?: string[];
  };
  services: {
    catering: boolean;
    photography: boolean;
    decoration: boolean;
    music: boolean;
    lighting: boolean;
    security: boolean;
  };
  timeline: {
    setupTime: string;
    eventStart: string;
    eventEnd: string;
    cleanupTime: string;
  };
  createdAt: string;
  updatedAt: string;
  notes?: string;
  staffNotes?: string;
}

export interface BookingFormData {
  eventType: Booking['eventType'];
  eventDate: string;
  eventTime: string;
  guestCount: number;
  packageType: Booking['packageType'];
  contactPerson: Booking['contactPerson'];
  eventDetails: Booking['eventDetails'];
  venue: Booking['venue'];
  services: Booking['services'];
  timeline: Booking['timeline'];
  specialRequests?: string;
}

export interface BookingFilter {
  status?: Booking['status'][];
  eventType?: Booking['eventType'][];
  dateRange?: {
    start: string;
    end: string;
  };
  paymentStatus?: Booking['paymentStatus'][];
}

export interface BookingStats {
  total: number;
  pending: number;
  confirmed: number;
  completed: number;
  cancelled: number;
  totalRevenue: number;
  pendingPayments: number;
}

export interface PaymentRecord {
  id: string;
  bookingId: string;
  amount: number;
  paymentMethod: 'credit_card' | 'bank_transfer' | 'cash' | 'check';
  transactionId?: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  processedAt: string;
  notes?: string;
}

export interface Message {
  id: string;
  bookingId?: string;
  fromUserId: string;
  toUserId: string;
  subject: string;
  content: string;
  isRead: boolean;
  sentAt: string;
  attachments?: {
    name: string;
    url: string;
    type: string;
  }[];
}

export interface Favorite {
  id: string;
  userId: string;
  type: 'service' | 'package' | 'gallery_image';
  itemId: string;
  itemData: {
    title: string;
    description?: string;
    imageUrl?: string;
    price?: number;
  };
  createdAt: string;
}

export interface UserPreferences {
  notifications: {
    email: boolean;
    sms: boolean;
    bookingReminders: boolean;
    promotionalEmails: boolean;
    eventUpdates: boolean;
  };
  privacy: {
    profileVisibility: 'public' | 'private';
    shareContactInfo: boolean;
    allowMarketing: boolean;
  };
  communication: {
    preferredContactMethod: 'email' | 'phone' | 'sms';
    timezone: string;
    language: 'en' | 'es';
  };
}
